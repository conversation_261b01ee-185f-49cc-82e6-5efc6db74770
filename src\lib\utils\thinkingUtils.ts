// Utilities for handling AI model thinking responses

export interface ProcessedResponse {
    content: string;
    thinking: string | null;
    hasThinking: boolean;
}

// Process AI response to extract thinking content and clean output
export function processThinkingResponse(rawResponse: string): ProcessedResponse {
    if (!rawResponse) {
        return { content: '', thinking: null, hasThinking: false };
    }

    // Look for thinking tags (case insensitive)
    const thinkingRegex = /<think>([\s\S]*?)<\/think>/gi;
    const matches = [...rawResponse.matchAll(thinkingRegex)];

    if (matches.length === 0) {
        return { content: rawResponse.trim(), thinking: null, hasThinking: false };
    }

    // Extract and combine thinking content
    const thinkingParts = matches
        .map(match => match[1].trim())
        .filter(part => part.length > 0);

    const thinking = thinkingParts.length > 0 ? thinkingParts.join('\n\n---\n\n') : null;
    const cleanContent = rawResponse.replace(thinkingRegex, '').trim();

    return {
        content: cleanContent,
        thinking,
        hasThinking: thinking !== null && thinking.length > 0
    };
}

// Format thinking content for display
export function formatThinkingContent(thinking: string): string {
    if (!thinking) return '';

    return thinking
        .split('\n\n')
        .map(paragraph => paragraph.trim())
        .filter(paragraph => paragraph.length > 0)
        .join('\n\n');
}
