/**
 * Utilities for handling thinking model responses
 */

export interface ProcessedResponse {
    content: string;
    thinking: string | null;
    hasThinking: boolean;
}

/**
 * Process AI response to extract thinking content and clean output
 * @param rawResponse The raw response from the AI model
 * @returns Processed response with separated thinking and content
 */
export function processThinkingResponse(rawResponse: string): ProcessedResponse {
    if (!rawResponse) {
        return {
            content: '',
            thinking: null,
            hasThinking: false
        };
    }

    // Look for thinking tags (case insensitive)
    const thinkingRegex = /<think>([\s\S]*?)<\/think>/gi;
    const matches = [...rawResponse.matchAll(thinkingRegex)];
    
    if (matches.length === 0) {
        // No thinking content found
        return {
            content: rawResponse.trim(),
            thinking: null,
            hasThinking: false
        };
    }

    // Extract all thinking content
    const thinkingParts = matches.map(match => match[1].trim()).filter(part => part.length > 0);
    const thinking = thinkingParts.length > 0 ? thinkingParts.join('\n\n---\n\n') : null;

    // Remove thinking tags and content from the response
    const cleanContent = rawResponse.replace(thinkingRegex, '').trim();

    return {
        content: cleanContent,
        thinking: thinking,
        hasThinking: thinking !== null && thinking.length > 0
    };
}

/**
 * Format thinking content for display
 * @param thinking The raw thinking content
 * @returns Formatted thinking content
 */
export function formatThinkingContent(thinking: string): string {
    if (!thinking) return '';
    
    // Add some basic formatting to make thinking content more readable
    return thinking
        .split('\n\n')
        .map(paragraph => paragraph.trim())
        .filter(paragraph => paragraph.length > 0)
        .join('\n\n');
}
