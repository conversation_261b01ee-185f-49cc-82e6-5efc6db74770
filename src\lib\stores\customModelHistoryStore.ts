import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import type { CustomModelHistory, CustomModelHistoryItem, AIProviderType } from '../types';
import { loadCustomModelHistory, saveCustomModelHistory } from '../services/storageService';
import { MODELS_WITH_DESCRIPTIONS } from '../config';

// Initialize with empty history
const initialHistory: CustomModelHistory = {};

export const customModelHistory = writable<CustomModelHistory>(initialHistory);

// Helper function to check if a model is a custom model (not a predefined one)
function isCustomModel(modelId: string): boolean {
    // Check if the model exists in any of the predefined model lists
    for (const provider of Object.keys(MODELS_WITH_DESCRIPTIONS) as AIProviderType[]) {
        const models = MODELS_WITH_DESCRIPTIONS[provider] || [];
        if (models.some(model => model.id === modelId)) {
            return false; // It's a predefined model
        }
    }
    return true; // It's a custom model
}

// Helper function to clean history of any predefined models
function cleanHistory(history: CustomModelHistory): CustomModelHistory {
    const cleanedHistory: CustomModelHistory = {};

    for (const [provider, items] of Object.entries(history)) {
        cleanedHistory[provider] = items.filter(item => isCustomModel(item.model));
    }

    return cleanedHistory;
}

// Function to load from storage
export function initializeCustomModelHistoryFromStorage() {
    if (browser) {
        const loaded = loadCustomModelHistory();
        // Clean any contaminated data (predefined models that got saved as custom)
        const cleaned = cleanHistory(loaded);
        customModelHistory.set(cleaned);

        // Save the cleaned data back to storage
        if (JSON.stringify(loaded) !== JSON.stringify(cleaned)) {
            saveCustomModelHistory(cleaned);
        }
    }
}

// Function to add a model to history for a specific provider
export function addModelToHistory(provider: AIProviderType, model: string) {
    if (!model || model.trim() === '') return;

    // Don't add predefined models to custom history
    if (!isCustomModel(model)) return;
    
    customModelHistory.update(history => {
        const providerHistory = history[provider] || [];
        
        // Remove existing entry if it exists
        const filteredHistory = providerHistory.filter(item => item.model !== model);
        
        // Add new entry at the beginning
        const newHistory = [
            { model: model.trim(), lastUsed: Date.now() },
            ...filteredHistory
        ];
        
        // Keep only the last 10 entries
        const limitedHistory = newHistory.slice(0, 10);
        
        const updatedHistory = {
            ...history,
            [provider]: limitedHistory
        };
        
        // Save to storage
        if (browser) {
            saveCustomModelHistory(updatedHistory);
        }
        
        return updatedHistory;
    });
}

// Function to remove a model from history for a specific provider
export function removeModelFromHistory(provider: AIProviderType, model: string) {
    customModelHistory.update(history => {
        const providerHistory = history[provider] || [];
        const filteredHistory = providerHistory.filter(item => item.model !== model);
        
        const updatedHistory = {
            ...history,
            [provider]: filteredHistory
        };
        
        // Save to storage
        if (browser) {
            saveCustomModelHistory(updatedHistory);
        }
        
        return updatedHistory;
    });
}

// Function to get history for a specific provider
export function getProviderHistory(provider: AIProviderType) {
    return derived(customModelHistory, $history => {
        return $history[provider] || [];
    });
}

// Function to clear all history for a provider
export function clearProviderHistory(provider: AIProviderType) {
    customModelHistory.update(history => {
        const updatedHistory = {
            ...history,
            [provider]: []
        };
        
        // Save to storage
        if (browser) {
            saveCustomModelHistory(updatedHistory);
        }
        
        return updatedHistory;
    });
}

// Derived store for custom provider history (most commonly used)
export const customProviderHistory = derived(customModelHistory, $history => {
    return $history['custom'] || [];
});
