import { writable } from 'svelte/store';
import type { StatusMessage } from '../types';

const initialStatus: StatusMessage | null = null;
export const status = writable<StatusMessage | null>(initialStatus);

let timeoutId: number | undefined;

export function showStatus(text: string, type: 'info' | 'success' | 'error' = 'info', duration: number = 5000) {
    if (timeoutId) {
        clearTimeout(timeoutId);
    }
    status.set({ text, type, id: Date.now() });
    timeoutId = window.setTimeout(() => {
        status.set(null);
    }, duration);
}