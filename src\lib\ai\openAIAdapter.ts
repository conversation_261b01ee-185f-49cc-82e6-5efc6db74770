import { AIProviderAdapter } from './aiProvider';
import { fetchWithTimeout } from './aiUtils';

export class OpenAIAdapter extends AIProviderAdapter {
    protected validateApiKey(): void {
        if (!this.apiKey) {
            throw new Error("API key is missing for this provider.");
        }
    }

    async generate(prompt: string): Promise<string> {
        // Check if API key is required for this adapter
        this.validateApiKey();

        // Construct the full URL - if customUrl is provided, append /chat/completions if not already present
        let url: string;
        if (this.customUrl) {
            // If customUrl already ends with /chat/completions, use as-is, otherwise append it
            url = this.customUrl.endsWith('/chat/completions')
                ? this.customUrl
                : `${this.customUrl.replace(/\/$/, '')}/chat/completions`;
        } else {
            url = 'https://api.openai.com/v1/chat/completions';
        }
        const headers: Record<string, string> = {
            ...this._getRequestHeaders(),
            'Authorization': `Bearer ${this.apiKey}`
        };

        // Add OpenRouter-specific headers if using OpenRouter
        if (this.customUrl && this.customUrl.includes('openrouter.ai')) {
            headers['HTTP-Referer'] = 'https://ai-notepad-app.local'; // Optional but recommended for OpenRouter
            headers['X-Title'] = 'AI Notepad'; // Optional but recommended for OpenRouter
        }

        const body = {
            model: this.model,
            messages: [{ role: "user", content: prompt }],
        };



        try {
            const response = await fetchWithTimeout(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(body)
            });



            if (!response.ok) {
                const responseText = await response.text();

                let errorData;
                try {
                    errorData = JSON.parse(responseText);
                } catch (parseError) {

                    // Check if it's an HTML response with specific error messages
                    if (responseText.includes('<!DOCTYPE html') || responseText.includes('<html')) {
                        if (responseText.includes('The model') && responseText.includes('is not available')) {
                            throw new Error(`Model not available on the API provider. Please check the model name and try again.`);
                        } else if (responseText.includes('not found') || responseText.includes('404')) {
                            throw new Error(`API endpoint not found. Please check the base URL configuration.`);
                        } else {
                            throw new Error(`API returned HTML instead of JSON. This usually indicates an incorrect endpoint URL.`);
                        }
                    }

                    throw new Error(`OpenAI/Custom API Error: ${response.status} - ${responseText.substring(0, 200)}`);
                }

                throw new Error(`OpenAI/Custom API Error: ${response.status} ${errorData.error?.message || response.statusText}`);
            }

            // Check content type first
            const contentType = response.headers.get('Content-Type') || '';
            const responseText = await response.text();

            // Check if we received HTML instead of JSON (even with 200 status)
            if (!contentType.includes('application/json') && (responseText.includes('<!DOCTYPE html') || responseText.includes('<html'))) {

                if (responseText.includes('The model') && responseText.includes('is not available')) {
                    throw new Error(`Model "${this.model}" is not available on the API provider. Please check the model name and try again.`);
                } else if (responseText.includes('not found') || responseText.includes('404')) {
                    throw new Error(`API endpoint not found. Please check the base URL configuration.`);
                } else {
                    throw new Error(`API returned HTML instead of JSON. This usually indicates an incorrect endpoint URL or configuration issue.`);
                }
            }

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
                throw new Error(`Invalid JSON response from API: ${errorMessage}`);
            }

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return data.choices[0].message.content;
            }
            throw new Error("Unexpected OpenAI/Custom API response structure.");
        } catch (error) {
            throw error;
        }
    }
}