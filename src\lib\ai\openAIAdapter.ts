import { AIProviderAdapter } from './aiProvider';

export class OpenAIAdapter extends AIProviderAdapter {
    async generate(prompt: string): Promise<string> {
        this.validateApiKey();

        const url = this.buildUrl();
        const headers = this.buildHeaders();
        const body = {
            model: this.model,
            messages: [{ role: "user", content: prompt }]
        };

        const data = await this.makeRequest(url, body, headers);

        // Handle OpenAI response structure
        if (data.choices?.[0]?.message?.content) {
            return data.choices[0].message.content;
        }

        throw new Error("Unexpected OpenAI API response structure.");
    }

    // Build the appropriate URL for OpenAI or custom endpoints
    private buildUrl(): string {
        if (this.customUrl) {
            return this.customUrl.endsWith('/chat/completions')
                ? this.customUrl
                : `${this.customUrl.replace(/\/$/, '')}/chat/completions`;
        }
        return 'https://api.openai.com/v1/chat/completions';
    }

    // Build headers with authentication and provider-specific options
    private buildHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'Authorization': `Bearer ${this.apiKey}`
        };

        // Add OpenRouter-specific headers if needed
        if (this.customUrl?.includes('openrouter.ai')) {
            headers['HTTP-Referer'] = 'https://ai-notepad-app.local';
            headers['X-Title'] = 'AI Notepad';
        }

        return headers;
    }

}