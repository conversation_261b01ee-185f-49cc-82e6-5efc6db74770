import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import type { ApiKeys } from '../types';
import { loadApiKeys as loadFromStorage, saveApiKeys as saveToStorage } from '../services/storageService';
import { DEFAULT_API_KEYS } from '../config';
import { showStatus } from './statusStore';

// Initialize with default keys, NOT by calling loadApiKeys() directly here
const initialKeys = { ...DEFAULT_API_KEYS };
export const apiKeys = writable<ApiKeys>(initialKeys);

// Function to explicitly load from storage, call this in onMount
export function initializeApiKeysFromStorage() {
    if (browser) {
        const loaded = loadFromStorage(); // This loadFromStorage should also be browser-guarded
        apiKeys.set(loaded);
    }
}

// Subscribe to changes for auto-saving IF NEEDED, also browser-guarded
if (browser) {
    apiKeys.subscribe(value => {
        // Consider if auto-save is too frequent.
        // An explicit save button is often better.
        // saveToStorage(value);
    });
}


export function updateApiKey<K extends keyof ApiKeys>(key: K, value: ApiKeys[K]) {
    apiKeys.update(keys => {
        const newKeys = { ...keys, [key]: value };
        // Auto-save to storage when keys are updated
        if (browser) {
            saveToStorage(newKeys);
        }
        return newKeys;
    });
}

export function saveApiKeysToStorage() {
    if (browser) { // Guard the save operation too
        apiKeys.subscribe(currentKeys => {
            saveToStorage(currentKeys);
            showStatus("API keys saved!", "success");
        })(); // Immediately invoke to get current value if not auto-subscribing for save
    } else {
        console.warn("Attempted to save API keys outside browser environment.");
    }
}