<script lang="ts">
  import { onMount } from 'svelte';
  import { appSettings, notepadContent, settingsLoaded } from '../stores/unifiedSettingsStore';
  import { callAIWithThinking } from '../services/apiService';
  import { showStatus } from '../stores/statusStore';
  import { getTextContext, extractSelectionContext, createAutocompletePrompt, createAnswerSelectionPrompt, extractCursorContext, createCursorInsertPrompt } from '../utils/textUtils';
  import ThinkingIndicator from './ThinkingIndicator.svelte';
  import type { ProcessedResponse } from '../utils/thinkingUtils';

  let editorElement: HTMLTextAreaElement;
  let isLoading = false;

  // Track thinking content for AI-generated text segments
  // Map from text content to thinking data
  let thinkingMap = new Map<string, string>();
  let currentThinking: string | null = null;
  let showThinkingPanel = false;

  onMount(() => {
    // Initialize with welcome message if content is empty
    if ($notepadContent.trim() === "") {
      notepadContent.set("Welcome to AI Pad!\n\nType something and try the AI features.");
    }
  });

  function handleInput() {
    if (editorElement) {
      notepadContent.set(editorElement.value);
    }
  }

  function insertTextAtCursor(text: string) {
    if (!editorElement) return;

    const startPos = editorElement.selectionStart;
    const endPos = editorElement.selectionEnd;
    const currentContent = $notepadContent;
    const beforeText = currentContent.substring(0, startPos);
    const afterText = currentContent.substring(endPos);

    const newContent = beforeText + text + afterText;
    notepadContent.set(newContent);

    // Update the textarea value
    editorElement.value = newContent;

    // Set cursor position after inserted text and select the inserted text
    const newCursorPos = startPos + text.length;
    editorElement.focus();
    editorElement.setSelectionRange(startPos, newCursorPos);
  }

  async function handleAutocomplete() {
    if (isLoading || !editorElement) return;

    const currentContent = $notepadContent;
    const context = getTextContext(currentContent, $appSettings.autocompleteContextLength);

    if (context.trim() === "") {
      showStatus("Notepad is empty or too short for autocomplete.", "error");
      return;
    }

    isLoading = true;
    const autocompletePrompt = createAutocompletePrompt(context);
    const aiResponse = await callAIWithThinking(autocompletePrompt);
    isLoading = false;

    if (aiResponse && aiResponse.content) {
      // Store thinking content if available
      if (aiResponse.hasThinking && aiResponse.thinking) {
        thinkingMap.set(aiResponse.content.trim(), aiResponse.thinking);
      }

      // Position cursor at the end and insert the response
      editorElement.focus();
      editorElement.setSelectionRange(currentContent.length, currentContent.length);
      insertTextAtCursor(" " + aiResponse.content.trim());
    }
  }

  async function handleAnswerSelection() {
    if (isLoading || !editorElement) return;
    
    const startPos = editorElement.selectionStart;
    const endPos = editorElement.selectionEnd;
    
    if (startPos === endPos) {
      showStatus("Please select some text to provide context for the AI.", "error");
      return;
    }

    // Calculate context lengths (2/3 before, 1/3 after)
    const totalContextLength = $appSettings.autocompleteContextLength;
    const beforeContextLength = Math.floor(totalContextLength * 2 / 3);
    const afterContextLength = Math.floor(totalContextLength * 1 / 3);

    // Extract context before and after selection
    const currentContent = $notepadContent;
    const { beforeText, afterText } = extractSelectionContext(
      currentContent,
      startPos,
      endPos,
      beforeContextLength,
      afterContextLength
    );

    const answerPrompt = createAnswerSelectionPrompt(beforeText, afterText);

    isLoading = true;
    const aiResponse = await callAIWithThinking(answerPrompt);
    isLoading = false;

    if (aiResponse && aiResponse.content) {
      // Store thinking content if available
      if (aiResponse.hasThinking && aiResponse.thinking) {
        thinkingMap.set(aiResponse.content.trim(), aiResponse.thinking);
      }

      // Replace the selected text with the AI response
      const beforeSelection = currentContent.substring(0, startPos);
      const afterSelection = currentContent.substring(endPos);

      const newContent = beforeSelection + aiResponse.content.trim() + afterSelection;
      notepadContent.set(newContent);
      editorElement.value = newContent;

      // Select the inserted text
      const newEndPos = startPos + aiResponse.content.trim().length;
      editorElement.focus();
      editorElement.setSelectionRange(startPos, newEndPos);
    }
  }

  async function handleInsertAtCursor() {
    if (isLoading || !editorElement) return;

    const cursorPos = editorElement.selectionStart;

    // Calculate context lengths (2/3 before, 1/3 after)
    const totalContextLength = $appSettings.autocompleteContextLength;
    const beforeContextLength = Math.floor(totalContextLength * 2 / 3);
    const afterContextLength = Math.floor(totalContextLength * 1 / 3);

    // Extract context before and after cursor
    const currentContent = $notepadContent;
    const { beforeText, afterText } = extractCursorContext(
      currentContent,
      cursorPos,
      beforeContextLength,
      afterContextLength
    );

    const insertPrompt = createCursorInsertPrompt(beforeText, afterText);

    isLoading = true;
    const aiResponse = await callAIWithThinking(insertPrompt);
    isLoading = false;

    if (aiResponse && aiResponse.content) {
      // Store thinking content if available
      if (aiResponse.hasThinking && aiResponse.thinking) {
        thinkingMap.set(aiResponse.content.trim(), aiResponse.thinking);
      }

      // Insert the AI response at the cursor position
      const beforeCursor = currentContent.substring(0, cursorPos);
      const afterCursor = currentContent.substring(cursorPos);

      const newContent = beforeCursor + aiResponse.content.trim() + afterCursor;
      notepadContent.set(newContent);
      editorElement.value = newContent;

      // Select the inserted text
      const newEndPos = cursorPos + aiResponse.content.trim().length;
      editorElement.focus();
      editorElement.setSelectionRange(cursorPos, newEndPos);
    }
  }

  function checkForThinkingContent() {
    if (!editorElement) return;

    const selectedText = editorElement.value.substring(
      editorElement.selectionStart,
      editorElement.selectionEnd
    ).trim();

    if (selectedText && thinkingMap.has(selectedText)) {
      currentThinking = thinkingMap.get(selectedText) || null;
      showThinkingPanel = true;
    } else {
      showThinkingPanel = false;
      currentThinking = null;
    }
  }

  function handleTextSelection() {
    checkForThinkingContent();
  }

  function toggleThinkingPanel() {
    if (thinkingMap.size > 0) {
      showThinkingPanel = !showThinkingPanel;
      if (showThinkingPanel && !currentThinking) {
        // Show the first available thinking content
        const firstThinking = thinkingMap.values().next().value;
        currentThinking = firstThinking || null;
      }
    }
  }
</script>

{#if $settingsLoaded}
<div class="notepad-area-container">
  <textarea
    bind:this={editorElement}
    bind:value={$notepadContent}
    class="notepad-editor"
    on:input={handleInput}
    on:select={handleTextSelection}
    on:click={handleTextSelection}
    style="font-family: {$appSettings.fontFamily}; font-size: {$appSettings.fontSize}px;"
    placeholder="Start typing your notes here..."
  ></textarea>
  <div class="notepad-buttons">
    <button on:click={handleAutocomplete} disabled={isLoading}>
      {isLoading ? 'Generating...' : 'Autocomplete'}
    </button>
    <button on:click={handleAnswerSelection} disabled={isLoading}>
      {isLoading ? 'Generating...' : 'Answer Selection'}
    </button>
    <button on:click={handleInsertAtCursor} disabled={isLoading}>
      {isLoading ? 'Generating...' : 'Insert at Cursor'}
    </button>
    {#if thinkingMap.size > 0}
      <button on:click={toggleThinkingPanel} class="thinking-toggle" title="View AI thinking process">
        🧠 Thinking ({thinkingMap.size})
      </button>
    {/if}
  </div>

  {#if showThinkingPanel && currentThinking}
    <div class="thinking-panel">
      <div class="thinking-header">
        <h4>AI Thinking Process</h4>
        <button on:click={() => showThinkingPanel = false} class="close-thinking">×</button>
      </div>
      <div class="thinking-content">
        <pre>{currentThinking}</pre>
      </div>
    </div>
  {/if}
</div>
{:else}
<div class="notepad-area-container">
  <div class="loading-placeholder">Loading notepad...</div>
</div>
{/if}

<style>
  .notepad-area-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
  }
  
  .notepad-editor {
    flex-grow: 1;
    border: none;
    outline: none;
    padding: 20px;
    resize: none;
    line-height: 1.6;
    background-color: #fafafa;
    border-right: 1px solid #ccc;
  }
  
  .notepad-buttons {
    display: flex;
    gap: 10px;
    padding: 10px 20px;
    background-color: #f0f0f0;
    border-top: 1px solid #ccc;
    border-right: 1px solid #ccc;
  }
  
  .notepad-buttons button {
    padding: 8px 16px;
    border: 1px solid #ccc;
    background-color: #fff;
    cursor: pointer;
    border-radius: 3px;
  }
  
  .notepad-buttons button:hover:not(:disabled) {
    background-color: #e9e9e9;
  }
  
  .notepad-buttons button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .thinking-toggle {
    background-color: #e8f5e8 !important;
    border-color: #4CAF50 !important;
    color: #2e7d32;
  }

  .thinking-toggle:hover:not(:disabled) {
    background-color: #c8e6c9 !important;
  }

  .thinking-panel {
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    margin: 10px 20px;
    max-height: 300px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .thinking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
  }

  .thinking-header h4 {
    margin: 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
  }

  .close-thinking {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
  }

  .close-thinking:hover {
    background-color: #e0e0e0;
  }

  .thinking-content {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
  }

  .thinking-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    line-height: 1.4;
    color: #555;
    margin: 0;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    border-left: 3px solid #4CAF50;
  }

  .loading-placeholder {
    padding: 40px 20px;
    text-align: center;
    color: #666;
    font-style: italic;
    font-size: 16px;
  }
</style>
