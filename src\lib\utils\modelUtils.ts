import { AVAILABLE_MODELS } from '../config';

// Check if a model is custom (not in predefined provider lists)
export function isCustomModel(model: string): boolean {
    if (!model) return false;

    // Check if model exists in any predefined provider list
    return !Object.values(AVAILABLE_MODELS).some(provider =>
        provider.some(m => m.id === model)
    );
}

// Get model description by ID for a specific provider
export function getModelDescription(provider: string, modelId: string): string | null {
    const models = AVAILABLE_MODELS[provider];
    return models?.find(m => m.id === modelId)?.name || null;
}

// Get all available models for a provider
export function getAvailableModels(provider: string) {
    return AVAILABLE_MODELS[provider] || [];
}
