import { AVAILABLE_MODELS } from '../config';

/**
 * Check if a model is a custom model (not in predefined lists)
 * @param model - The model string to check
 * @returns true if the model is custom, false if it's predefined
 */
export function isCustomModel(model: string): boolean {
    if (!model) return false;

    // Check if the model exists in any of the predefined provider lists
    for (const provider of Object.values(AVAILABLE_MODELS)) {
        if (provider.some(m => m.id === model)) {
            return false;
        }
    }

    return true;
}
