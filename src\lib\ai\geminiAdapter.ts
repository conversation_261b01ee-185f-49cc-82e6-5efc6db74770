import { AIProviderAdapter } from './aiProvider';
import { fetchWithTimeout } from './aiUtils';

export class GeminiAdapter extends AIProviderAdapter {
    async generate(prompt: string): Promise<string> {
        if (!this.apiKey) throw new Error("Gemini API key is missing.");

        const url = `https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`;
        const body = {
            contents: [{ parts: [{ text: prompt }] }],
        };

        try {
            const response = await fetchWithTimeout(url, {
                method: 'POST',
                headers: this._getRequestHeaders(),
                body: JSON.stringify(body)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ error: { message: "Unknown Gemini API error" } }));
                console.error("Gemini API Error:", errorData);
                throw new Error(`Gemini API Error: ${response.status} ${errorData.error?.message || response.statusText}`);
            }
            const data = await response.json();
            if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
                return data.candidates[0].content.parts[0].text;
            } else if (data.promptFeedback && data.promptFeedback.blockReason) {
                 throw new Error(`Gemini API Blocked: ${data.promptFeedback.blockReason} - ${data.promptFeedback.blockReasonMessage || ''}`);
            }
            throw new Error("Unexpected Gemini API response structure.");
        } catch (error) {
            console.error("Error calling Gemini API:", error);
            throw error;
        }
    }
}