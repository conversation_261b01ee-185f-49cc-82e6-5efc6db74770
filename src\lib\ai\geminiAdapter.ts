import { AIProviderAdapter } from './aiProvider';

export class GeminiAdapter extends AIProviderAdapter {
    async generate(prompt: string): Promise<string> {
        this.validateApiKey();

        const url = `https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`;
        const body = {
            contents: [{ parts: [{ text: prompt }] }]
        };

        const data = await this.makeRequest(url, body);

        // Handle Gemini-specific response structure
        if (data.candidates?.[0]?.content?.parts?.[0]?.text) {
            return data.candidates[0].content.parts[0].text;
        }

        // Handle content blocking
        if (data.promptFeedback?.blockReason) {
            throw new Error(`Gemini blocked request: ${data.promptFeedback.blockReason} - ${data.promptFeedback.blockReasonMessage || ''}`);
        }

        throw new Error("Unexpected Gemini API response structure.");
    }
}