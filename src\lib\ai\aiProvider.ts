import type { AI<PERSON><PERSON>ider } from '../types';
import { fetchWithTimeout } from './aiUtils';

// Base adapter with common functionality
export abstract class AIProviderAdapter implements AIProvider {
    constructor(
        protected apiKey: string | null,
        protected model: string,
        protected customUrl: string | null = null
    ) {}

    abstract generate(prompt: string): Promise<string>;

    // Validate API key (can be overridden by subclasses)
    protected validateApiKey(): void {
        if (!this.apiKey) {
            throw new Error("API key is required for this provider.");
        }
    }

    // Get base request headers
    protected getRequestHeaders(): Record<string, string> {
        return {
            'Content-Type': 'application/json'
        };
    }

    // Common error handling for API responses
    protected handleApiError(response: Response, responseText: string): never {
        // Check for HTML responses (common error case)
        if (responseText.includes('<!DOCTYPE html') || responseText.includes('<html')) {
            if (responseText.includes('The model') && responseText.includes('is not available')) {
                throw new Error(`Model "${this.model}" is not available. Please check the model name.`);
            }
            if (responseText.includes('not found') || responseText.includes('404')) {
                throw new Error(`API endpoint not found. Please check the URL configuration.`);
            }
            throw new Error(`API returned HTML instead of JSON. Check the endpoint URL.`);
        }

        // Try to parse JSON error
        try {
            const errorData = JSON.parse(responseText);
            const message = errorData.error?.message || response.statusText;
            throw new Error(`API Error: ${response.status} ${message}`);
        } catch {
            throw new Error(`API Error: ${response.status} - ${responseText.substring(0, 200)}`);
        }
    }

    // Common method to make API requests
    protected async makeRequest(url: string, body: any, headers: Record<string, string> = {}): Promise<any> {
        const requestHeaders = { ...this.getRequestHeaders(), ...headers };

        try {
            const response = await fetchWithTimeout(url, {
                method: 'POST',
                headers: requestHeaders,
                body: JSON.stringify(body)
            });

            const responseText = await response.text();

            if (!response.ok) {
                this.handleApiError(response, responseText);
            }

            // Validate JSON response
            const contentType = response.headers.get('Content-Type') || '';
            if (!contentType.includes('application/json') &&
                (responseText.includes('<!DOCTYPE html') || responseText.includes('<html'))) {
                this.handleApiError(response, responseText);
            }

            try {
                return JSON.parse(responseText);
            } catch (parseError) {
                throw new Error(`Invalid JSON response: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`);
            }
        } catch (error) {
            throw error;
        }
    }
}