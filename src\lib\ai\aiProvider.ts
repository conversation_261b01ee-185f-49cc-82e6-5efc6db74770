import type { AIProvider } from '../types';

export abstract class AIProviderAdapter implements AIProvider {
    constructor(protected apiKey: string | null, protected model: string, protected customUrl: string | null = null) {}

    abstract generate(prompt: string): Promise<string>;

    protected _getRequestHeaders(): Record<string, string> {
        return {
            'Content-Type': 'application/json'
        };
    }
}