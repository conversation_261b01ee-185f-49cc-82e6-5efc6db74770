<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { customModelHistory, addModelToHistory, removeModelFromHistory, appSettings } from '../stores/unifiedSettingsStore';
  import type { CustomModelHistoryItem } from '../types';

  export let value: string = '';
  export let placeholder: string = 'Enter custom model name';

  const dispatch = createEventDispatcher();

  let isOpen = false;
  let inputElement: HTMLInputElement;
  let dropdownElement: HTMLDivElement;
  let filteredHistory: CustomModelHistoryItem[] = [];

  // Get history for the current provider (should be 'custom' when this component is used)
  $: currentProviderHistory = $customModelHistory[$appSettings.aiProvider] || [];

  $: {
    // Filter history based on current input
    if (value.trim() === '') {
      filteredHistory = currentProviderHistory;
    } else {
      filteredHistory = currentProviderHistory.filter(item =>
        item.model.toLowerCase().includes(value.toLowerCase())
      );
    }
  }

  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    value = target.value;
    dispatch('input', value);
    
    // Open dropdown when typing
    if (!isOpen && value.length > 0) {
      isOpen = true;
    }
  }

  function handleChange() {
    dispatch('change', value);
  }

  function selectModel(model: string) {
    value = model;
    isOpen = false;
    dispatch('input', value);
    dispatch('change', value);
    inputElement.focus();
  }

  function removeModel(event: Event, model: string) {
    event.stopPropagation();
    removeModelFromHistory($appSettings.aiProvider, model);
  }

  function toggleDropdown() {
    isOpen = !isOpen;
    if (isOpen) {
      inputElement.focus();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      isOpen = false;
    } else if (event.key === 'ArrowDown' && !isOpen) {
      isOpen = true;
    }
  }

  function handleClickOutside(event: MouseEvent) {
    if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
      isOpen = false;
    }
  }

  onMount(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  });
</script>

<div class="custom-model-select" bind:this={dropdownElement}>
  <div class="input-container">
    <input
      bind:this={inputElement}
      type="text"
      {placeholder}
      {value}
      on:input={handleInput}
      on:change={handleChange}
      on:keydown={handleKeydown}
      on:focus={() => isOpen = true}
      class="model-input"
    />
    <button
      type="button"
      class="dropdown-arrow"
      class:open={isOpen}
      on:click={toggleDropdown}
      aria-label="Toggle dropdown"
    >
      ▼
    </button>
  </div>

  {#if isOpen && (filteredHistory.length > 0 || value.trim() !== '')}
    <div class="dropdown">
      {#if filteredHistory.length > 0}
        <div class="dropdown-section">
          <div class="section-header">Recent Models</div>
          {#each filteredHistory as item (item.model)}
            <div
              class="dropdown-item"
              role="button"
              tabindex="0"
              on:click={() => selectModel(item.model)}
              on:keydown={(e) => e.key === 'Enter' && selectModel(item.model)}
            >
              <span class="model-name">{item.model}</span>
              <button
                type="button"
                class="remove-button"
                on:click={(e) => removeModel(e, item.model)}
                aria-label="Remove from history"
              >
                ×
              </button>
            </div>
          {/each}
        </div>
      {/if}
      
      {#if value.trim() !== '' && !filteredHistory.some(item => item.model === value.trim())}
        <div class="dropdown-section">
          <div class="section-header">New Model</div>
          <div
            class="dropdown-item new-model"
            role="button"
            tabindex="0"
            on:click={() => selectModel(value.trim())}
            on:keydown={(e) => e.key === 'Enter' && selectModel(value.trim())}
          >
            <span class="model-name">Use "{value.trim()}"</span>
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .custom-model-select {
    position: relative;
    width: 100%;
  }

  .input-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .model-input {
    width: 100%;
    padding: 8px 32px 8px 8px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 14px;
  }

  .model-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  .dropdown-arrow {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    padding: 4px;
    transition: transform 0.2s ease;
  }

  .dropdown-arrow:hover {
    color: #333;
  }

  .dropdown-arrow.open {
    transform: rotate(180deg);
  }

  .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 3px 3px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
  }

  .dropdown-section {
    border-bottom: 1px solid #eee;
  }

  .dropdown-section:last-child {
    border-bottom: none;
  }

  .section-header {
    padding: 8px 12px 4px;
    font-size: 11px;
    font-weight: bold;
    color: #666;
    text-transform: uppercase;
    background-color: #f8f9fa;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
  }

  .dropdown-item:last-child {
    border-bottom: none;
  }

  .dropdown-item:hover {
    background-color: #f8f9fa;
  }

  .dropdown-item.new-model {
    background-color: #e8f4fd;
  }

  .dropdown-item.new-model:hover {
    background-color: #d1ecf1;
  }

  .model-name {
    flex: 1;
    font-size: 14px;
    color: #333;
  }

  .remove-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #999;
    padding: 2px 6px;
    margin-left: 8px;
    border-radius: 2px;
    line-height: 1;
  }

  .remove-button:hover {
    background-color: #ff4444;
    color: white;
  }
</style>
