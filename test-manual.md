# Manual Testing Guide for AI Notepad

## Test Setup
1. Open the application at http://localhost:5174/
2. Make sure you have API keys configured in the sidebar
3. Test both autocomplete and answer selection features

## Test Cases

### 1. Autocomplete Feature Test
**Steps:**
1. Clear the notepad content
2. Type some text (e.g., "The weather today is beautiful and sunny. I decided to go for a walk in the park because")
3. Click the "Autocomplete" button
4. Verify that:
   - AI generates a continuation that makes sense
   - The generated text is automatically selected
   - The text flows naturally from the existing content
   - No HTML tags are visible in the output

### 2. Answer Selection Feature Test
**Steps:**
1. Type a longer text with a gap, like: "The cat sat on the mat. [SELECT THIS PART] The dog barked loudly."
2. Select the text "[SELECT THIS PART]"
3. Click "Answer Selection" button
4. Verify that:
   - AI generates text that connects the before and after parts
   - The selected text is replaced with AI-generated content
   - The generated text is automatically selected
   - The text flows naturally between the before and after parts

### 3. Insert at Cursor Feature Test
**Steps:**
1. Type text with a gap where you want to insert, like: "The weather was beautiful today. I decided to go outside."
2. Place your cursor between the two sentences (after the period and space)
3. Click "Insert at Cursor" button
4. Verify that:
   - AI generates text that fits naturally at the cursor position
   - The text is inserted at the exact cursor location
   - The generated text is automatically selected
   - The text flows naturally with the surrounding content

### 4. Settings Test
**Steps:**
1. Go to the sidebar settings
2. Change the autocomplete context length (try 500, 2000)
3. Test autocomplete with different context lengths
4. Verify that shorter context uses less text for AI prompts

### 5. Plain Text Verification
**Steps:**
1. Try typing special characters: <, >, &, quotes
2. Verify that all characters appear as plain text
3. Verify that no HTML formatting is applied
4. Copy and paste content to verify it's plain text

## Expected Results
- All AI features work without errors
- Text insertion and selection work smoothly
- No HTML tags or formatting appear in the editor
- Settings are properly applied
- Content is saved and restored correctly
