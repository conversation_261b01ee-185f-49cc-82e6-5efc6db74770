// Core AI provider types
export type AIProviderType = 'gemini' | 'openai' | 'custom';
export type StatusType = 'info' | 'success' | 'error';

// API configuration for all providers
export interface ApiKeys {
    gemini: string;
    openai: string;
    customUrl: string;
    customKey: string;
}

// Main application settings
export interface AppSettings {
    fontFamily: string;
    fontSize: string;
    aiProvider: AIProviderType;
    aiModel: string;
    autocompleteContextLength: number;
}

// AI provider interface - all adapters must implement this
export interface AIProvider {
    generate(prompt: string): Promise<string>;
}

// Status message for user feedback
export interface StatusMessage {
    text: string;
    type: StatusType;
    id: number;
}

// Model history tracking for custom models
export interface ModelHistoryItem {
    model: string;
    lastUsed: number;
}

// Last selected models per provider (for state restoration)
export interface LastSelectedModels {
    gemini: string;
    openai: string;
    custom: string;
}

// Custom model history organized by provider
export interface CustomModelHistory {
    [provider: string]: ModelHistoryItem[];
}