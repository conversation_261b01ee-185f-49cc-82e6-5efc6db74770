export type AIProviderType = 'gemini' | 'openai' | 'custom';

export interface ApiKeys {
    gemini: string;
    openai: string;
    customUrl: string;
    customKey: string;
}

export interface AppSettings {
    fontFamily: string;
    fontSize: string;
    aiProvider: AIProviderType;
    aiModel: string; // This will be the model ID
    autocompleteContextLength: number; // Number of characters to use for autocomplete context
}

export interface AIProvider {
    generate(prompt: string): Promise<string>;
}

export interface StatusMessage {
    text: string;
    type: 'info' | 'success' | 'error';
    id: number; // For managing multiple messages if needed
}

export interface CustomModelHistoryItem {
    model: string;
    lastUsed: number; // timestamp
}

// Storage for last selected models for each provider type
export interface LastSelectedModels {
    gemini: string;
    openai: string;
    custom: string;
}

export interface CustomModelHistory {
    [provider: string]: CustomModelHistoryItem[];
}