export interface ModelConfig {
    id: string;
    name: string; // User-friendly name / description
}

export const AUTOCOMPLETE_CONTEXT_LENGTH = 1000;
export const API_TIMEOUT = 30000; // 30 seconds

export const MODELS_WITH_DESCRIPTIONS: Record<string, ModelConfig[]> = {
    gemini: [
        { id: "gemini-2.5-flash-preview-04-17", name: "Gemini 2.5 Flash (Preview 04-17) - Very Fast, Experimental" },
        { id: "gemini-2.5-pro-preview-05-06", name: "Gemini 2.5 Pro (Preview 05-06) - Powerful, Experimental" },
        { id: "gemini-2.0-flash", name: "Gemini 2.0 Flash - Fast, Balanced" },
        { id: "gemini-2.0-flash-lite", name: "Gemini 2.0 Flash Lite - Very Fast, Lightweight" },
        { id: "gemini-1.5-flash", name: "Gemini 1.5 Flash - Fast, Efficient, Good Value (Often latest)" }, // You might have gemini-1.5-flash-latest as an alias in the API
        { id: "gemini-1.5-flash-8b", name: "Gemini 1.5 Flash (8B) - Compact & Fast" },
        { id: "gemini-1.5-pro", name: "Gemini 1.5 Pro - Advanced, Large Context" },
    ],
    openai: [
        { id: "gpt-4o-mini", name: "GPT-4o mini - Fast, Cost-Effective, Multimodal" },
        { id: "gpt-4o", name: "GPT-4o - Flagship, Intelligent, Multimodal" },
        { id: "gpt-3.5-turbo", name: "GPT-3.5 Turbo - Fast, Affordable, Text-Focused" },
    ],
    custom: [] // For custom, model is a text input, so no predefined descriptions
};

export const DEFAULT_SETTINGS = {
    fontFamily: 'Arial, sans-serif',
    fontSize: '16',
    aiProvider: 'openai' as const,
    aiModel: MODELS_WITH_DESCRIPTIONS.openai[0].id
};

export const DEFAULT_APP_SETTINGS: AppSettings = {
    fontFamily: 'Arial, sans-serif',
    fontSize: '16',
    aiProvider: 'openai',
    aiModel: MODELS_WITH_DESCRIPTIONS.openai[0].id,
    autocompleteContextLength: 1000
};

export const DEFAULT_API_KEYS: ApiKeys = {
    gemini: '',
    openai: '',
    customUrl: '',
    customKey: ''
};

export const DEFAULT_LAST_SELECTED_MODELS = {
    gemini: MODELS_WITH_DESCRIPTIONS.gemini[0].id,
    openai: MODELS_WITH_DESCRIPTIONS.openai[0].id,
    custom: ''
};

export const NOTEPAD_CONTENT_STORAGE_KEY = 'aiNotepadSvelteContent';
export const API_KEYS_STORAGE_KEY = 'aiNotepadSvelteApiKeys';
export const APP_SETTINGS_STORAGE_KEY = 'aiNotepadSvelteSettings';
export const CUSTOM_MODEL_HISTORY_STORAGE_KEY = 'aiNotepadSvelteCustomModelHistory';
export const LAST_CUSTOM_MODELS_STORAGE_KEY = 'aiNotepadSvelteLastCustomModels';
export const LAST_SELECTED_MODELS_STORAGE_KEY = 'aiNotepadSvelteLastSelectedModels';