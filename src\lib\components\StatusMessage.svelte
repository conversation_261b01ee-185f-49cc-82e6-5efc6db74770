<script lang="ts">
  import { status } from '../stores/statusStore';
</script>

{#if $status}
  <div class="status-message status-{$status.type}">
    {$status.text}
  </div>
{/if}

<style>
  .status-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.9em;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    min-width: 200px;
    text-align: center;
  }
  .status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  .status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  .status-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
  }
</style>