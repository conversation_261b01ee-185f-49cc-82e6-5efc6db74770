<script lang="ts">
  import { formatThinkingContent } from '../utils/thinkingUtils';

  export let thinking: string;
  export let position: { x: number; y: number } = { x: 0, y: 0 };
  
  let showThinking = false;
  let thinkingModal: HTMLDivElement;

  function toggleThinking() {
    showThinking = !showThinking;
  }

  function closeThinking() {
    showThinking = false;
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      closeThinking();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeThinking();
    }
  }

  $: formattedThinking = formatThinkingContent(thinking);
</script>

<svelte:window on:keydown={handleKeydown} />

<button 
  class="thinking-indicator"
  on:click={toggleThinking}
  title="View AI thinking process"
  aria-label="View AI thinking process"
>
  🧠
</button>

{#if showThinking}
  <div class="thinking-modal-backdrop" on:click={handleBackdropClick}>
    <div class="thinking-modal" bind:this={thinkingModal}>
      <div class="thinking-header">
        <h3>AI Thinking Process</h3>
        <button class="close-button" on:click={closeThinking} aria-label="Close thinking">
          ×
        </button>
      </div>
      <div class="thinking-content">
        <pre>{formattedThinking}</pre>
      </div>
    </div>
  </div>
{/if}

<style>
  .thinking-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border: none;
    background: rgba(76, 175, 80, 0.1);
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    margin-left: 4px;
    transition: all 0.2s ease;
    vertical-align: middle;
  }

  .thinking-indicator:hover {
    background: rgba(76, 175, 80, 0.2);
    transform: scale(1.1);
  }

  .thinking-indicator:active {
    transform: scale(0.95);
  }

  .thinking-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .thinking-modal {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 700px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
  }

  .thinking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
  }

  .thinking-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
  }

  .close-button:hover {
    background-color: #e0e0e0;
  }

  .thinking-content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
  }

  .thinking-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    margin: 0;
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #4CAF50;
  }
</style>
