import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import type { AppSettings, ApiKeys, CustomModelHistory, AIProviderType, LastSelectedModels } from '../types';
import { loadUnifiedSettings, saveUnifiedSettings } from '../services/storageService';
import { DEFAULT_APP_SETTINGS, DEFAULT_API_KEYS, DEFAULT_LAST_SELECTED_MODELS, AVAILABLE_MODELS } from '../config';
import { isCustomModel } from '../utils/modelUtils';

// Settings state management
export const settingsLoaded = writable(false);
let isInitialized = false;
let allowAutoSave = false;

// Core settings stores
export const appSettings = writable<AppSettings>({ ...DEFAULT_APP_SETTINGS });
export const apiKeys = writable<ApiKeys>({ ...DEFAULT_API_KEYS });
export const notepadContent = writable<string>("");
export const customModelHistory = writable<CustomModelHistory>({ gemini: [], openai: [], custom: [] });
export const lastCustomModels = writable<Record<AIProviderType, string>>({ gemini: '', openai: '', custom: '' });
export const lastSelectedModels = writable<LastSelectedModels>({ ...DEFAULT_LAST_SELECTED_MODELS });

// Combined settings store for easy access
export const unifiedSettings = derived(
    [appSettings, apiKeys, notepadContent, customModelHistory, lastCustomModels, lastSelectedModels],
    ([$appSettings, $apiKeys, $notepadContent, $customModelHistory, $lastCustomModels, $lastSelectedModels]) => ({
        appSettings: $appSettings,
        apiKeys: $apiKeys,
        notepadContent: $notepadContent,
        customModelHistory: $customModelHistory,
        lastCustomModels: $lastCustomModels,
        lastSelectedModels: $lastSelectedModels
    })
);

// Validate and fix app settings to ensure consistency
function validateAppSettings(settings: AppSettings): AppSettings {
    const validSettings = { ...settings };

    // For predefined providers, ensure the selected model exists
    if (validSettings.aiProvider !== 'custom') {
        const availableModels = AVAILABLE_MODELS[validSettings.aiProvider] || [];
        const modelExists = availableModels.some(m => m.id === validSettings.aiModel);

        if (!modelExists && availableModels.length > 0) {
            validSettings.aiModel = availableModels[0].id;
        }
    }

    return validSettings;
}



// Restore model selection for a provider
function restoreModelSelection(settings: AppSettings, loaded: any): AppSettings {
    const validatedSettings = { ...settings };

    if (validatedSettings.aiProvider !== 'custom') {
        // For predefined providers, restore from lastSelectedModels
        const savedModel = loaded.lastSelectedModels[validatedSettings.aiProvider];
        if (savedModel) {
            const availableModels = AVAILABLE_MODELS[validatedSettings.aiProvider] || [];
            if (availableModels.some(m => m.id === savedModel)) {
                validatedSettings.aiModel = savedModel;
            }
        }
    } else {
        // For custom provider, restore from lastCustomModels or history
        const savedCustomModel = loaded.lastCustomModels[validatedSettings.aiProvider];
        if (savedCustomModel && isCustomModel(savedCustomModel)) {
            validatedSettings.aiModel = savedCustomModel;
        } else {
            // Fallback to most recent from history
            const history = loaded.customModelHistory[validatedSettings.aiProvider] || [];
            if (history.length > 0 && isCustomModel(history[0].model)) {
                validatedSettings.aiModel = history[0].model;
            }
        }
    }

    return validatedSettings;
}

// Clean up custom model data to prevent contamination
function cleanCustomModels(loaded: any): Record<AIProviderType, string> {
    const cleaned: Record<AIProviderType, string> = { gemini: '', openai: '', custom: '' };

    for (const [provider, model] of Object.entries(loaded.lastCustomModels) as [AIProviderType, string][]) {
        if (!model || isCustomModel(model)) {
            cleaned[provider] = model || '';
        }
    }

    return cleaned;
}

// Initialize all settings from storage
export function initializeAllSettings(): Promise<void> {
    // Prevent multiple initializations
    if (isInitialized) return Promise.resolve();

    if (!browser) {
        settingsLoaded.set(true);
        isInitialized = true;
        return Promise.resolve();
    }

    isInitialized = true;

    try {
        const loaded = loadUnifiedSettings();

        // Validate and restore app settings
        let validatedSettings = validateAppSettings(loaded.appSettings);
        validatedSettings = restoreModelSelection(validatedSettings, loaded);

        // Set all stores
        appSettings.set(validatedSettings);
        apiKeys.set(loaded.apiKeys);
        notepadContent.set(loaded.notepadContent);
        customModelHistory.set(loaded.customModelHistory);
        lastSelectedModels.set(loaded.lastSelectedModels);

        // Clean and set custom models
        const cleanedCustomModels = cleanCustomModels(loaded);
        lastCustomModels.set(cleanedCustomModels);

        // Save if data was cleaned
        if (JSON.stringify(loaded.lastCustomModels) !== JSON.stringify(cleanedCustomModels)) {
            saveUnifiedSettings({ ...loaded, lastCustomModels: cleanedCustomModels });
        }

        settingsLoaded.set(true);

        // Enable auto-save after initialization
        setTimeout(setupAutoSave, 50);

        return Promise.resolve();
    } catch (error) {
        console.error('Failed to initialize settings:', error);
        settingsLoaded.set(true);
        return Promise.resolve();
    }
}

// Auto-save settings when any store changes
function autoSaveSettings() {
    if (!browser || !allowAutoSave || !get(settingsLoaded)) return;

    const currentSettings = get(unifiedSettings);
    saveUnifiedSettings(currentSettings);
}

// Enable auto-save for all stores
export function setupAutoSave() {
    if (!browser || allowAutoSave) return;

    allowAutoSave = true;

    // Subscribe to all stores for automatic saving
    const stores = [appSettings, apiKeys, notepadContent, customModelHistory, lastCustomModels, lastSelectedModels];
    stores.forEach(store => store.subscribe(autoSaveSettings));
}

// Update a specific app setting
export function updateAppSetting<K extends keyof AppSettings>(key: K, value: AppSettings[K]) {
    appSettings.update(settings => ({ ...settings, [key]: value }));

    // Save model selection for predefined providers
    if (key === 'aiModel') {
        const currentSettings = get(appSettings);
        if (currentSettings.aiProvider !== 'custom' && value) {
            lastSelectedModels.update(models => ({
                ...models,
                [currentSettings.aiProvider]: value as string
            }));
        }
    }
}

// Update a specific API key
export function updateApiKey<K extends keyof ApiKeys>(key: K, value: ApiKeys[K]) {
    apiKeys.update(keys => ({ ...keys, [key]: value }));
}

// Update notepad content
export function updateNotepadContent(content: string) {
    notepadContent.set(content);
}

// Get available models for current provider
export const availableModels = derived(appSettings, $appSettings => {
    return $appSettings.aiProvider === 'custom' ? [] : AVAILABLE_MODELS[$appSettings.aiProvider] || [];
});

// Get description of currently selected model
export const currentModelDescription = derived([appSettings, availableModels], ([$appSettings, $availableModels]) => {
    if ($appSettings.aiProvider === 'custom') return null;
    return $availableModels.find(m => m.id === $appSettings.aiModel)?.name || null;
});

// Save current model selection before switching providers
function saveCurrentModelSelection(currentSettings: AppSettings) {
    if (!currentSettings.aiModel) return;

    if (currentSettings.aiProvider === 'custom') {
        // Save custom models separately
        if (isCustomModel(currentSettings.aiModel)) {
            lastCustomModels.update(models => ({
                ...models,
                [currentSettings.aiProvider]: currentSettings.aiModel
            }));
        }
    } else {
        // Save predefined provider models
        lastSelectedModels.update(models => ({
            ...models,
            [currentSettings.aiProvider]: currentSettings.aiModel
        }));
    }
}

// Restore model selection for the new provider
function restoreModelForProvider(newProvider: AIProviderType): string {
    if (newProvider === 'custom') {
        // Try to restore custom model
        const lastCustom = get(lastCustomModels)[newProvider];
        if (lastCustom) return lastCustom;

        // Fallback to history
        const history = get(customModelHistory)[newProvider] || [];
        return history.length > 0 && isCustomModel(history[0].model) ? history[0].model : '';
    } else {
        // Try to restore predefined model
        const lastSelected = get(lastSelectedModels)[newProvider];
        const availableModels = AVAILABLE_MODELS[newProvider] || [];

        if (lastSelected && availableModels.some(m => m.id === lastSelected)) {
            return lastSelected;
        }

        // Fallback to first available model
        return availableModels.length > 0 ? availableModels[0].id : '';
    }
}

// Switch AI provider and restore appropriate model
export function switchAIProvider(newProvider: AIProviderType) {
    const currentSettings = get(appSettings);
    if (currentSettings.aiProvider === newProvider) return;

    // Save current selection
    saveCurrentModelSelection(currentSettings);

    // Switch provider and restore model
    const newModel = restoreModelForProvider(newProvider);
    appSettings.set({
        ...currentSettings,
        aiProvider: newProvider,
        aiModel: newModel
    });
}

// Add custom model to history (max 10 items)
export function addModelToHistory(provider: AIProviderType, model: string) {
    if (!model || !isCustomModel(model)) return;

    customModelHistory.update(history => {
        const providerHistory = [...(history[provider] || [])];

        // Remove existing entry if present
        const existingIndex = providerHistory.findIndex(item => item.model === model);
        if (existingIndex >= 0) {
            providerHistory.splice(existingIndex, 1);
        }

        // Add to front with current timestamp
        providerHistory.unshift({ model, lastUsed: Date.now() });

        // Keep only last 10 items
        return {
            ...history,
            [provider]: providerHistory.slice(0, 10)
        };
    });
}

// Remove model from history
export function removeModelFromHistory(provider: AIProviderType, model: string) {
    customModelHistory.update(history => ({
        ...history,
        [provider]: (history[provider] || []).filter(item => item.model !== model)
    }));
}
