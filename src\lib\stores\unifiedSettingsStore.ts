import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import type { AppSettings, ApiKeys, CustomModelHistory, AIProviderType, LastSelectedModels } from '../types';
import { loadUnifiedSettings, saveUnifiedSettings, type UnifiedSettings } from '../services/storageService';
import { DEFAULT_APP_SETTINGS, DEFAULT_API_KEYS, DEFAULT_LAST_SELECTED_MODELS, MODELS_WITH_DESCRIPTIONS } from '../config';
import { isCustomModel } from '../utils/modelUtils';

// Settings loading state
export const settingsLoaded = writable(false);
export const settingsLoading = writable(false);

// Flag to prevent multiple initializations
let isInitialized = false;
// Flag to prevent auto-save during initialization
let allowAutoSave = false;

// Individual setting stores - initialize with defaults but don't trigger auto-save yet
export const appSettings = writable<AppSettings>({ ...DEFAULT_APP_SETTINGS });
export const apiKeys = writable<ApiKeys>({ ...DEFAULT_API_KEYS });
export const notepadContent = writable<string>("");
export const customModelHistory = writable<CustomModelHistory>({ gemini: [], openai: [], custom: [] });
export const lastCustomModels = writable<Record<AIProviderType, string>>({ gemini: '', openai: '', custom: '' });
export const lastSelectedModels = writable<LastSelectedModels>({ ...DEFAULT_LAST_SELECTED_MODELS });

// Unified settings store that combines all settings
export const unifiedSettings = derived(
    [appSettings, apiKeys, notepadContent, customModelHistory, lastCustomModels, lastSelectedModels],
    ([$appSettings, $apiKeys, $notepadContent, $customModelHistory, $lastCustomModels, $lastSelectedModels]) => ({
        appSettings: $appSettings,
        apiKeys: $apiKeys,
        notepadContent: $notepadContent,
        customModelHistory: $customModelHistory,
        lastCustomModels: $lastCustomModels,
        lastSelectedModels: $lastSelectedModels
    })
);

// Validation function for app settings
function validateAppSettings(settings: AppSettings): AppSettings {
    let validSettings = { ...settings };
    if (validSettings.aiProvider !== 'custom') {
        const providerModels = MODELS_WITH_DESCRIPTIONS[validSettings.aiProvider] || [];
        if (!providerModels.some(m => m.id === validSettings.aiModel) && providerModels.length > 0) {
            validSettings.aiModel = providerModels[0].id;
        } else if (providerModels.length === 0) {
            validSettings.aiModel = '';
        }
    }
    return validSettings;
}



// Initialize all settings from storage synchronously
export function initializeAllSettings(): Promise<void> {
    // Prevent multiple initializations
    if (isInitialized) {
        return Promise.resolve();
    }

    if (!browser) {
        settingsLoaded.set(true);
        isInitialized = true;
        return Promise.resolve();
    }

    isInitialized = true;
    settingsLoading.set(true);

    try {
        const loaded = loadUnifiedSettings();

        // Validate and set app settings first
        let validatedAppSettings = validateAppSettings(loaded.appSettings);

        // Restore the saved model selection for the current provider
        if (validatedAppSettings.aiProvider !== 'custom') {
            // For predefined providers, try to restore from lastSelectedModels
            const savedModel = loaded.lastSelectedModels[validatedAppSettings.aiProvider];
            if (savedModel && savedModel !== '') {
                const providerModels = MODELS_WITH_DESCRIPTIONS[validatedAppSettings.aiProvider] || [];
                if (providerModels.some(m => m.id === savedModel)) {
                    validatedAppSettings.aiModel = savedModel;
                }
            }
        } else {
            // For custom provider, try to restore from lastCustomModels or history
            const savedCustomModel = loaded.lastCustomModels[validatedAppSettings.aiProvider];
            if (savedCustomModel && savedCustomModel !== '' && isCustomModel(savedCustomModel)) {
                validatedAppSettings.aiModel = savedCustomModel;
            } else {
                // Try to get from history if no saved custom model
                const history = loaded.customModelHistory[validatedAppSettings.aiProvider] || [];
                if (history.length > 0 && isCustomModel(history[0].model)) {
                    validatedAppSettings.aiModel = history[0].model;
                }
            }
        }

        appSettings.set(validatedAppSettings);

        // Set other settings
        apiKeys.set(loaded.apiKeys);
        notepadContent.set(loaded.notepadContent);
        customModelHistory.set(loaded.customModelHistory);
        lastSelectedModels.set(loaded.lastSelectedModels);

        // Clean last custom models (remove any contaminated predefined models)
        const cleanedLastModels: Record<AIProviderType, string> = {
            gemini: '',
            openai: '',
            custom: ''
        };

        for (const [provider, model] of Object.entries(loaded.lastCustomModels) as [AIProviderType, string][]) {
            if (!model || isCustomModel(model)) {
                cleanedLastModels[provider] = model || '';
            }
        }

        lastCustomModels.set(cleanedLastModels);

        // Only save if the cleaned data is different (avoid unnecessary saves)
        if (JSON.stringify(loaded.lastCustomModels) !== JSON.stringify(cleanedLastModels)) {
            const updatedSettings = { ...loaded, lastCustomModels: cleanedLastModels };
            saveUnifiedSettings(updatedSettings);
        }

        settingsLoaded.set(true);
        settingsLoading.set(false);

        // Set up auto-save after initialization is complete
        setTimeout(() => {
            setupAutoSave();
        }, 50);

        return Promise.resolve();
    } catch (error) {
        console.error('Failed to initialize settings:', error);
        settingsLoaded.set(true);
        settingsLoading.set(false);
        return Promise.resolve();
    }
}

// Auto-save function that saves all settings when any change occurs
function autoSaveSettings() {
    if (!browser) return;

    // Only auto-save if settings have been loaded and auto-save is enabled
    if (!allowAutoSave || !get(settingsLoaded) || !isInitialized) {
        return;
    }

    const currentSettings = get(unifiedSettings);
    saveUnifiedSettings(currentSettings);
}

// Set up auto-save subscriptions only after initialization
export function setupAutoSave() {
    if (!browser || allowAutoSave) return;

    allowAutoSave = true;

    // Subscribe to all individual stores for auto-save
    appSettings.subscribe(autoSaveSettings);
    apiKeys.subscribe(autoSaveSettings);
    notepadContent.subscribe(autoSaveSettings);
    customModelHistory.subscribe(autoSaveSettings);
    lastCustomModels.subscribe(autoSaveSettings);
    lastSelectedModels.subscribe(autoSaveSettings);
}

// Helper functions for updating individual settings
export function updateAppSetting<K extends keyof AppSettings>(key: K, value: AppSettings[K]) {
    appSettings.update(settings => ({ ...settings, [key]: value }));

    // If updating the AI model for a predefined provider, also save it to lastSelectedModels
    if (key === 'aiModel') {
        const currentSettings = get(appSettings);
        if (currentSettings.aiProvider !== 'custom' && value) {
            lastSelectedModels.update(models => ({
                ...models,
                [currentSettings.aiProvider]: value as string
            }));
        }
    }
}

export function updateApiKey<K extends keyof ApiKeys>(key: K, value: ApiKeys[K]) {
    apiKeys.update(keys => ({ ...keys, [key]: value }));
}

export function updateNotepadContent(content: string) {
    notepadContent.set(content);
}

// Derived stores for convenience
export const availableModels = derived(appSettings, $appSettings => {
    if ($appSettings.aiProvider === 'custom') {
        return [];
    }
    return MODELS_WITH_DESCRIPTIONS[$appSettings.aiProvider] || [];
});

export const currentModelDescription = derived([appSettings, availableModels], ([$appSettings, $availableModels]) => {
    if ($appSettings.aiProvider === 'custom') return null;
    const model = $availableModels.find(m => m.id === $appSettings.aiModel);
    return model?.name || null;
});

// Provider switching logic with model state preservation for all providers
export function switchAIProvider(newProvider: AIProviderType) {
    const currentSettings = get(appSettings);

    if (currentSettings.aiProvider === newProvider) return;

    // Save current model selection for the current provider
    let updatedLastSelectedModels = get(lastSelectedModels);
    let updatedLastCustomModels = get(lastCustomModels);

    if (currentSettings.aiModel) {
        if (currentSettings.aiProvider === 'custom') {
            // For custom provider, save to lastCustomModels if it's a custom model
            if (isCustomModel(currentSettings.aiModel)) {
                updatedLastCustomModels = {
                    ...updatedLastCustomModels,
                    [currentSettings.aiProvider]: currentSettings.aiModel
                };
                lastCustomModels.set(updatedLastCustomModels);
            }
        } else {
            // For predefined providers (gemini, openai), save to lastSelectedModels
            updatedLastSelectedModels = {
                ...updatedLastSelectedModels,
                [currentSettings.aiProvider]: currentSettings.aiModel
            };
            lastSelectedModels.set(updatedLastSelectedModels);
        }
    }

    // Update provider
    let newSettings = { ...currentSettings, aiProvider: newProvider };

    // Restore model selection for the new provider
    if (newProvider === 'custom') {
        // For custom provider, restore from lastCustomModels or history
        const lastModel = updatedLastCustomModels[newProvider];
        if (lastModel !== undefined && lastModel !== '') {
            newSettings.aiModel = lastModel;
        } else {
            // Try to get from history if no saved state
            const history = get(customModelHistory)[newProvider] || [];
            if (history.length > 0 && isCustomModel(history[0].model)) {
                newSettings.aiModel = history[0].model;
            } else {
                newSettings.aiModel = '';
            }
        }
    } else {
        // For predefined providers, restore from lastSelectedModels or use default
        const lastModel = updatedLastSelectedModels[newProvider];
        if (lastModel && lastModel !== '') {
            // Verify the model still exists for this provider
            const providerModels = MODELS_WITH_DESCRIPTIONS[newProvider] || [];
            if (providerModels.some(m => m.id === lastModel)) {
                newSettings.aiModel = lastModel;
            } else {
                // Model no longer exists, use first available
                newSettings.aiModel = providerModels.length > 0 ? providerModels[0].id : '';
            }
        } else {
            // No saved model, use first available
            const providerModels = MODELS_WITH_DESCRIPTIONS[newProvider] || [];
            newSettings.aiModel = providerModels.length > 0 ? providerModels[0].id : '';
        }
    }

    appSettings.set(newSettings);
}

// Function to add model to history (for custom models)
export function addModelToHistory(provider: AIProviderType, model: string) {
    if (!model || !isCustomModel(model)) return;

    customModelHistory.update(history => {
        const providerHistory = history[provider] || [];
        const existingIndex = providerHistory.findIndex(item => item.model === model);

        if (existingIndex >= 0) {
            // Update timestamp and move to front
            providerHistory.splice(existingIndex, 1);
        }

        // Add to front with current timestamp
        providerHistory.unshift({ model, lastUsed: Date.now() });

        // Keep only last 10 items
        if (providerHistory.length > 10) {
            providerHistory.splice(10);
        }

        return { ...history, [provider]: providerHistory };
    });
}

// Function to remove model from history
export function removeModelFromHistory(provider: AIProviderType, model: string) {
    customModelHistory.update(history => {
        const providerHistory = history[provider] || [];
        const filteredHistory = providerHistory.filter(item => item.model !== model);

        return { ...history, [provider]: filteredHistory };
    });
}
