<script lang="ts">
  import ApiKeysModal from './ApiKeysModal.svelte';

  let showApiKeysModal = false;

  function openApiKeysModal() {
    showApiKeysModal = true;
  }

  function closeApiKeysModal() {
    showApiKeysModal = false;
  }
</script>

<header class="app-header">
  <div class="header-logo">AI<span>Pad</span></div>
  <div class="api-controls">
    <button class="api-keys-button" on:click={openApiKeysModal}>
      🔑 API Keys
    </button>
  </div>
</header>

<ApiKeysModal bind:isOpen={showApiKeysModal} on:close={closeApiKeysModal} />

<style>
  .app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #333;
    color: white;
    flex-shrink: 0;
  }
  .header-logo {
    font-size: 1.5em;
    font-weight: bold;
  }
  .header-logo span {
    color: #4CAF50;
  }
  .api-controls {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .api-keys-button {
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s;
  }
  .api-keys-button:hover {
    background-color: #45a049;
  }
</style>