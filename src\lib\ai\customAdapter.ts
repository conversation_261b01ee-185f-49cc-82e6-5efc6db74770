import { OpenAIAdapter } from './openAIAdapter'; // Re-use OpenAIAdapter logic

export class Custom<PERSON><PERSON>pter extends OpenAIAdapter {
    constructor(apiKey: string | null, model: string, customUrl: string) {
        if (!customUrl) throw new Error("Custom API URL is mandatory for CustomAdapter.");
        super(apiKey, model, customUrl); // Pass customUrl to OpenAIAdapter constructor
    }

    // Override API key validation for custom adapters
    // Some custom APIs might not require an API key, or it might be optional
    protected validateApiKey(): void {
        // For custom adapters, we'll be more lenient with API key validation
        // If no API key is provided, we'll still attempt the request
        // The actual API will return an error if authentication is required
        if (!this.apiKey) {
            console.warn("No API key provided for custom adapter. Some APIs may require authentication.");
        }
    }

    // generate method is inherited from OpenAIAdapter
    // It will use this.customUrl passed in the constructor
}