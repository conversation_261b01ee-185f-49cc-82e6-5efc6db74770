import { OpenAIAdapter } from './openAIAdapter';

// Custom adapter that extends OpenAI adapter for custom endpoints
export class Custom<PERSON><PERSON>pter extends OpenAIAdapter {
    constructor(apiKey: string | null, model: string, customUrl: string) {
        if (!customUrl) {
            throw new Error("Custom API URL is required for CustomAdapter.");
        }
        super(apiKey, model, customUrl);
    }

    // Override API key validation - custom APIs may not require authentication
    protected validateApiKey(): void {
        if (!this.apiKey) {
            console.warn("No API key provided for custom adapter. Some APIs may require authentication.");
        }
        // Don't throw error - let the API decide if auth is required
    }
}