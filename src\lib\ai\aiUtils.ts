import { API_TIMEOUT } from '../config';

export async function fetchWithTimeout(
    resource: RequestInfo | URL,
    options: RequestInit = {},
    timeout: number = API_TIMEOUT
): Promise<Response> {
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(resource, {
        ...options,
        signal: controller.signal
    });
    clearTimeout(id);
    return response;
}