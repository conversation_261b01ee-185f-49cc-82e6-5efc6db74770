import { browser } from '$app/environment';
import type { ApiKeys, AppSettings, CustomModelHistory, AIProviderType, LastSelectedModels } from '../types';
import {
    DEFAULT_API_KEYS,
    DEFAULT_APP_SETTINGS,
    DEFAULT_LAST_SELECTED_MODELS,
    STORAGE_KEYS
} from '../config';

// Unified settings interface for all persistent data
export interface UnifiedSettings {
    appSettings: AppSettings;
    apiKeys: ApiKeys;
    notepadContent: string;
    customModelHistory: CustomModelHistory;
    lastCustomModels: Record<AIProviderType, string>;
    lastSelectedModels: LastSelectedModels;
}

// Storage keys for unified settings
export const UNIFIED_SETTINGS_STORAGE_KEY = 'aiNotepadSvelteUnifiedSettings';

// Helper function to safely parse JSON with fallback
function safeJsonParse<T>(jsonString: string | null, fallback: T): T {
    if (!jsonString) return fallback;
    try {
        return JSON.parse(jsonString);
    } catch (e) {
        console.error("Failed to parse JSO<PERSON> from localStorage", e);
        return fallback;
    }
}

// Helper function to safely stringify and save to localStorage
function safeSaveToStorage(key: string, data: any): boolean {
    if (!browser) return false;
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error(`Failed to save ${key} to localStorage`, e);
        return false;
    }
}

// Unified settings loading and saving functions
export function loadUnifiedSettings(): UnifiedSettings {
    if (!browser) {
        return {
            appSettings: { ...DEFAULT_APP_SETTINGS },
            apiKeys: { ...DEFAULT_API_KEYS },
            notepadContent: "",
            customModelHistory: { gemini: [], openai: [], custom: [] },
            lastCustomModels: { gemini: '', openai: '', custom: '' },
            lastSelectedModels: { ...DEFAULT_LAST_SELECTED_MODELS }
        };
    }

    // Try to load unified settings first
    const unifiedStored = localStorage.getItem(UNIFIED_SETTINGS_STORAGE_KEY);

    if (unifiedStored) {
        try {
            const parsed = JSON.parse(unifiedStored);

            // Ensure all required properties exist with defaults - create completely new objects
            const mergedAppSettings = {
                fontFamily: parsed.appSettings?.fontFamily || DEFAULT_APP_SETTINGS.fontFamily,
                fontSize: parsed.appSettings?.fontSize || DEFAULT_APP_SETTINGS.fontSize,
                aiProvider: parsed.appSettings?.aiProvider || DEFAULT_APP_SETTINGS.aiProvider,
                aiModel: parsed.appSettings?.aiModel || DEFAULT_APP_SETTINGS.aiModel,
                autocompleteContextLength: parsed.appSettings?.autocompleteContextLength || DEFAULT_APP_SETTINGS.autocompleteContextLength
            };

            const result = {
                appSettings: mergedAppSettings,
                apiKeys: {
                    gemini: parsed.apiKeys?.gemini || DEFAULT_API_KEYS.gemini,
                    openai: parsed.apiKeys?.openai || DEFAULT_API_KEYS.openai,
                    customUrl: parsed.apiKeys?.customUrl || DEFAULT_API_KEYS.customUrl,
                    customKey: parsed.apiKeys?.customKey || DEFAULT_API_KEYS.customKey
                },
                notepadContent: parsed.notepadContent || "",
                customModelHistory: parsed.customModelHistory || { gemini: [], openai: [], custom: [] },
                lastCustomModels: parsed.lastCustomModels || { gemini: '', openai: '', custom: '' },
                lastSelectedModels: parsed.lastSelectedModels || { ...DEFAULT_LAST_SELECTED_MODELS }
            };
            return result;
        } catch (e) {
            console.error("Failed to parse unified settings, falling back to individual keys", e);
        }
    }

    // Fallback to individual storage keys for migration
    const result = {
        appSettings: safeJsonParse(localStorage.getItem(STORAGE_KEYS.APP_SETTINGS), { ...DEFAULT_APP_SETTINGS }),
        apiKeys: safeJsonParse(localStorage.getItem(STORAGE_KEYS.API_KEYS), { ...DEFAULT_API_KEYS }),
        notepadContent: localStorage.getItem(STORAGE_KEYS.NOTEPAD_CONTENT) || "",
        customModelHistory: safeJsonParse(localStorage.getItem(STORAGE_KEYS.CUSTOM_MODEL_HISTORY), { gemini: [], openai: [], custom: [] }),
        lastCustomModels: safeJsonParse(localStorage.getItem(STORAGE_KEYS.LAST_CUSTOM_MODELS), { gemini: '', openai: '', custom: '' }),
        lastSelectedModels: safeJsonParse(localStorage.getItem(STORAGE_KEYS.LAST_SELECTED_MODELS), { ...DEFAULT_LAST_SELECTED_MODELS })
    };
    return result;
}

export function saveUnifiedSettings(settings: UnifiedSettings): boolean {
    const success = safeSaveToStorage(UNIFIED_SETTINGS_STORAGE_KEY, settings);

    // Also save to individual keys for backward compatibility
    if (success) {
        safeSaveToStorage(STORAGE_KEYS.APP_SETTINGS, settings.appSettings);
        safeSaveToStorage(STORAGE_KEYS.API_KEYS, settings.apiKeys);
        safeSaveToStorage(STORAGE_KEYS.NOTEPAD_CONTENT, settings.notepadContent);
        safeSaveToStorage(STORAGE_KEYS.CUSTOM_MODEL_HISTORY, settings.customModelHistory);
        safeSaveToStorage(STORAGE_KEYS.LAST_CUSTOM_MODELS, settings.lastCustomModels);
        safeSaveToStorage(STORAGE_KEYS.LAST_SELECTED_MODELS, settings.lastSelectedModels);
    }

    return success;
}

// Legacy functions for backward compatibility
export function loadApiKeys(): ApiKeys {
    return loadUnifiedSettings().apiKeys;
}

export function saveApiKeys(keys: ApiKeys): void {
    if (!browser) return;
    const unified = loadUnifiedSettings();
    unified.apiKeys = keys;
    saveUnifiedSettings(unified);
}

export function loadAppSettings(): AppSettings {
    return loadUnifiedSettings().appSettings;
}

export function saveAppSettings(settings: AppSettings): void {
    if (!browser) return;
    const unified = loadUnifiedSettings();
    unified.appSettings = settings;
    saveUnifiedSettings(unified);
}

export function loadNotepadContent(): string {
    return loadUnifiedSettings().notepadContent;
}

export function saveNotepadContent(content: string): void {
    if (!browser) return;
    const unified = loadUnifiedSettings();
    unified.notepadContent = content;
    saveUnifiedSettings(unified);
}

export function loadCustomModelHistory(): CustomModelHistory {
    return loadUnifiedSettings().customModelHistory;
}

export function saveCustomModelHistory(history: CustomModelHistory): void {
    if (!browser) return;
    const unified = loadUnifiedSettings();
    unified.customModelHistory = history;
    saveUnifiedSettings(unified);
}

export function loadLastCustomModels(): Record<AIProviderType, string> {
    return loadUnifiedSettings().lastCustomModels;
}

export function saveLastCustomModels(models: Record<AIProviderType, string>): void {
    if (!browser) return;
    const unified = loadUnifiedSettings();
    unified.lastCustomModels = models;
    saveUnifiedSettings(unified);
}