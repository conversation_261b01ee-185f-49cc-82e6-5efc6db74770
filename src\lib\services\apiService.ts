import type { AIProvider as AIProviderInterface, ApiKeys, AppSettings, AIProviderType } from '../types';
import { GeminiAdapter } from '../ai/geminiAdapter';
import { OpenAIAdapter } from '../ai/openAIAdapter';
import { CustomAdapter } from '../ai/customAdapter';
import { showStatus } from '../stores/statusStore';
import { MODELS_WITH_DESCRIPTIONS } from '../config';
import type { ModelConfig } from '../config';
import { processThinkingResponse, type ProcessedResponse } from '../utils/thinkingUtils';

// Helper to get store values non-reactively if needed, or pass stores/values directly
import { get } from 'svelte/store';
import { apiKeys as apiKeysStore, appSettings as appSettingsStore } from '../stores/unifiedSettingsStore';


export async function callAIWithThinking(prompt: string): Promise<ProcessedResponse | null> {
    const currentApiKeys = get(apiKeysStore);
    const currentAppSettings = get(appSettingsStore);

    const { aiProvider, aiModel: modelIdFromSettings } = currentAppSettings;
    let effectiveModelId = modelIdFromSettings;

    // Validation (similar to previous version)
    if (aiProvider === 'custom') {
        if (!effectiveModelId || effectiveModelId.trim() === '') {
            showStatus("Custom model name cannot be empty.", "error");
            return null;
        }
    } else {
        const providerModels: ModelConfig[] = MODELS_WITH_DESCRIPTIONS[aiProvider] || [];
        const modelExists = providerModels.some(m => m.id === effectiveModelId);
        if (!effectiveModelId || !modelExists) {
            if (providerModels.length > 0) {
                effectiveModelId = providerModels[0].id;
                console.warn(`Model ID '${modelIdFromSettings}' for ${aiProvider} invalid. Defaulting to '${effectiveModelId}'.`);
            } else {
                showStatus(`No models configured for provider: ${aiProvider}.`, "error");
                return null;
            }
        }
    }

    let adapter: AIProviderInterface;
    let apiKeyForProvider: string | null = null;
    let customUrlForProvider: string | null = null;

    switch (aiProvider) {
        case 'gemini':
            apiKeyForProvider = currentApiKeys.gemini;
            if (!apiKeyForProvider) { showStatus("Gemini API Key is missing.", "error"); return null; }
            adapter = new GeminiAdapter(apiKeyForProvider, effectiveModelId);
            break;
        case 'openai':
            apiKeyForProvider = currentApiKeys.openai;
            if (!apiKeyForProvider) { showStatus("OpenAI API Key is missing.", "error"); return null; }
            adapter = new OpenAIAdapter(apiKeyForProvider, effectiveModelId);
            break;
        case 'custom':
            apiKeyForProvider = currentApiKeys.customKey; // Can be null
            customUrlForProvider = currentApiKeys.customUrl;
            if (!customUrlForProvider) { showStatus("Custom API URL is missing.", "error"); return null; }
            adapter = new CustomAdapter(apiKeyForProvider, effectiveModelId, customUrlForProvider);
            break;
        default:
            showStatus(`Unsupported AI provider: ${aiProvider}`, "error");
            return null;
    }

    showStatus(`Calling AI (${aiProvider}, ${effectiveModelId})...`, 'info');
    // Loading state can be managed by a store or component-local variable

    try {
        const rawResult = await adapter.generate(prompt);
        showStatus("AI response received.", "success");

        // Process the response to extract thinking content
        const processedResponse = processThinkingResponse(rawResult);
        return processedResponse;
    } catch (error: any) {
        console.error("AI Call Error:", error);
        showStatus(`AI Error: ${error.message}`, "error");
        return null;
    }
}

// Backward-compatible function that returns only the content
export async function callAI(prompt: string): Promise<string | null> {
    const result = await callAIWithThinking(prompt);
    return result ? result.content : null;
}