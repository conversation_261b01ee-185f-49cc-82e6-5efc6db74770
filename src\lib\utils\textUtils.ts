// Text manipulation utilities for the notepad

// Get text context with optional maximum length
export function getTextContext(text: string, maxLength?: number): string {
  return maxLength !== undefined ? text.slice(-maxLength) : text;
}

// Extract context around a text selection
export function extractSelectionContext(
  fullText: string,
  selectionStart: number,
  selectionEnd: number,
  beforeContextLength: number,
  afterContextLength: number
): { beforeText: string; afterText: string; selectedText: string } {
  return {
    selectedText: fullText.substring(selectionStart, selectionEnd),
    beforeText: fullText.substring(
      Math.max(0, selectionStart - beforeContextLength),
      selectionStart
    ),
    afterText: fullText.substring(
      selectionEnd,
      selectionEnd + afterContextLength
    )
  };
}

// Extract context around cursor position
export function extractCursorContext(
  fullText: string,
  cursorPosition: number,
  beforeContextLength: number,
  afterContextLength: number
): { beforeText: string; afterText: string } {
  return {
    beforeText: fullText.substring(
      Math.max(0, cursorPosition - beforeContextLength),
      cursorPosition
    ),
    afterText: fullText.substring(
      cursorPosition,
      cursorPosition + afterContextLength
    )
  };
}

// Create autocomplete prompt
export function createAutocompletePrompt(context: string): string {
  return `Continue writing the following text in the same style and tone. Only provide the continuation, do not repeat the existing text:\n\n${context}`;
}

// Create prompt for filling text between two segments
export function createAnswerSelectionPrompt(beforeText: string, afterText: string): string {
  return `Provide text that would naturally fit between the following two text segments. Write in the same style and tone as the surrounding text. Only provide the connecting text, nothing else.

Text before:
${beforeText}

Text after:
${afterText}

Provide the text that should go between these segments:`;
}

// Create prompt for inserting text at cursor position
export function createCursorInsertPrompt(beforeText: string, afterText: string): string {
  return `Provide text that would naturally fit at the cursor position between the following two text segments. Write in the same style and tone as the surrounding text. Only provide the text to insert, nothing else.

Text before cursor:
${beforeText}

Text after cursor:
${afterText}

Provide the text to insert at the cursor position:`;
}
