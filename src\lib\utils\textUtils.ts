/**
 * Utility functions for text manipulation in the notepad
 */

/**
 * Get text context with optional maximum length
 */
export function getTextContext(text: string, maxLength?: number): string {
  if (maxLength !== undefined) {
    return text.slice(-maxLength);
  }
  return text;
}

/**
 * Extract context before and after a selection with specified lengths
 */
export function extractSelectionContext(
  fullText: string,
  selectionStart: number,
  selectionEnd: number,
  beforeContextLength: number,
  afterContextLength: number
): { beforeText: string; afterText: string; selectedText: string } {
  const selectedText = fullText.substring(selectionStart, selectionEnd);
  const beforeText = fullText.substring(
    Math.max(0, selectionStart - beforeContextLength),
    selectionStart
  );
  const afterText = fullText.substring(
    selectionEnd,
    selectionEnd + afterContextLength
  );
  
  return { beforeText, afterText, selectedText };
}

/**
 * Create autocomplete prompt
 */
export function createAutocompletePrompt(context: string): string {
  return `Continue writing the following text in the same style and tone. Only provide the continuation, do not repeat the existing text:\n\n${context}`;
}

/**
 * Create answer selection prompt
 */
export function createAnswerSelectionPrompt(beforeText: string, afterText: string): string {
  return `Provide text that would naturally fit between the following two text segments. Write in the same style and tone as the surrounding text. Only provide the connecting text, nothing else.

Text before:
${beforeText}

Text after:
${afterText}

Provide the text that should go between these segments:`;
}

/**
 * Extract context before and after a cursor position
 */
export function extractCursorContext(
  fullText: string,
  cursorPosition: number,
  beforeContextLength: number,
  afterContextLength: number
): { beforeText: string; afterText: string } {
  const beforeText = fullText.substring(
    Math.max(0, cursorPosition - beforeContextLength),
    cursorPosition
  );
  const afterText = fullText.substring(
    cursorPosition,
    cursorPosition + afterContextLength
  );

  return { beforeText, afterText };
}

/**
 * Create cursor insertion prompt
 */
export function createCursorInsertPrompt(beforeText: string, afterText: string): string {
  return `Provide text that would naturally fit at the cursor position between the following two text segments. Write in the same style and tone as the surrounding text. Only provide the text to insert, nothing else.

Text before cursor:
${beforeText}

Text after cursor:
${afterText}

Provide the text to insert at the cursor position:`;
}
